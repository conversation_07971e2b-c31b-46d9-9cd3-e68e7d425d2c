/**
 * 中文语言检测工具类
 * 统一单订单和多订单的中文检测逻辑
 * <AUTHOR>
 * @version 1.0.0
 */

// 确保OTA命名空间存在
window.OTA = window.OTA || {};

(function() {
    'use strict';

    /**
     * 中文语言检测器类
     * 提供统一的中文字符检测和语言选择逻辑
     */
    class ChineseLanguageDetector {
        constructor() {
            // 中文字符检测正则表达式（Unicode范围：U+4E00-U+9FFF）
            this.chineseRegex = /[\u4e00-\u9fff]/;
            
            // 语言ID映射
            this.languageIds = {
                ENGLISH: 2,
                CHINESE: 4
            };
            
            // 日志记录器
            this.logger = null;
        }

        /**
         * 初始化检测器
         */
        init() {
            this.logger = getLogger();
            this.logger?.log('中文语言检测器已初始化', 'info');
        }

        /**
         * 检测文本中是否包含中文字符
         * @param {string} text - 待检测的文本
         * @returns {boolean} 是否包含中文字符
         */
        detectChinese(text) {
            if (!text || typeof text !== 'string') {
                return false;
            }
            return this.chineseRegex.test(text);
        }

        /**
         * 根据文本内容确定推荐的语言ID
         * @param {string} text - 待检测的文本
         * @returns {number} 推荐的语言ID
         */
        getRecommendedLanguageId(text) {
            const hasChinese = this.detectChinese(text);
            return hasChinese ? this.languageIds.CHINESE : this.languageIds.ENGLISH;
        }

        /**
         * 获取语言名称
         * @param {number} languageId - 语言ID
         * @returns {string} 语言名称
         */
        getLanguageName(languageId) {
            switch (languageId) {
                case this.languageIds.CHINESE:
                    return '中文 (Chinese)';
                case this.languageIds.ENGLISH:
                    return '英文 (English)';
                default:
                    return '未知语言';
            }
        }

        /**
         * 检测并获取完整的语言信息
         * @param {string} text - 待检测的文本
         * @returns {Object} 检测结果对象
         */
        detectLanguageInfo(text) {
            const hasChinese = this.detectChinese(text);
            const languageId = hasChinese ? this.languageIds.CHINESE : this.languageIds.ENGLISH;
            
            return {
                hasChinese: hasChinese,
                recommendedLanguageId: languageId,
                languageName: this.getLanguageName(languageId),
                languagesIdArray: [languageId]
            };
        }

        /**
         * 单订单模式：检测并设置UI中的语言选择
         * @param {string} text - 输入文本
         * @returns {boolean} 是否成功设置
         */
        detectAndSetUILanguage(text) {
            try {
                const detection = this.detectLanguageInfo(text);
                
                // 获取语言下拉组件
                const languageDropdown = window.OTA?.MultiSelectDropdown;
                const languagesContainer = document.getElementById('languagesDropdown');
                
                if (!languageDropdown || !languagesContainer) {
                    this.logger?.log('语言下拉组件未找到，跳过自动语言选择', 'warning');
                    return false;
                }
                
                // 获取下拉实例 - 多种访问路径
                let dropdownInstance = languagesContainer._multiSelectInstance;
                if (!dropdownInstance) {
                    dropdownInstance = window.languageDropdownInstance;
                }
                if (!dropdownInstance && window.uiManager?.languagesDropdown) {
                    dropdownInstance = window.uiManager.languagesDropdown;
                }
                if (!dropdownInstance && window.formManager?.languagesDropdown) {
                    dropdownInstance = window.formManager.languagesDropdown;
                }
                
                if (dropdownInstance) {
                    dropdownInstance.setSelectedValues([detection.recommendedLanguageId.toString()]);
                    
                    this.logger?.log(
                        `${detection.hasChinese ? '检测到中文字符，自动选择中文语言' : '未检测到中文字符，默认选择英文语言'} (语言ID: ${detection.recommendedLanguageId})`, 
                        'info'
                    );
                    
                    // 验证设置是否成功
                    const currentValues = dropdownInstance.getSelectedValues();
                    this.logger?.log(`语言设置验证: 当前选中值为 [${currentValues.join(', ')}]`, 'info');
                    
                    return true;
                } else {
                    this.logger?.log('语言下拉实例未找到，跳过自动语言选择。检查路径: 容器实例、全局实例、UIManager实例、FormManager实例', 'warning');
                    return false;
                }
                
            } catch (error) {
                this.logger?.logError('UI语言检测设置失败', error);
                return false;
            }
        }

        /**
         * 多订单模式：处理订单数组的语言检测
         * @param {Array} orders - 订单数组
         * @returns {Array} 处理后的订单数组
         */
        processOrdersLanguageDetection(orders) {
            try {
                this.logger?.log('🔍 开始多订单中文语言自动检测...', 'info');
                
                if (!Array.isArray(orders)) {
                    this.logger?.log('⚠️ 输入不是有效的订单数组', 'warning');
                    return orders;
                }
                
                return orders.map((order, index) => {
                    // 检测文本源：优先使用rawText，备用customerName
                    const orderText = order.rawText || order.customerName || '';
                    const detection = this.detectLanguageInfo(orderText);
                    
                    // 克隆订单对象，避免修改原始数据
                    const processedOrder = { ...order };
                    
                    if (detection.hasChinese) {
                        // 包含中文，选择中文语言
                        processedOrder.languagesIdArray = [this.languageIds.CHINESE];
                        this.logger?.log(`✅ 订单${index + 1}检测到中文字符，自动选择中文语言`, 'info');
                    } else {
                        // 不包含中文，默认选择英文（仅在未设置语言的情况下）
                        if (!processedOrder.languagesIdArray || processedOrder.languagesIdArray.length === 0) {
                            processedOrder.languagesIdArray = [this.languageIds.ENGLISH];
                            this.logger?.log(`🔤 订单${index + 1}未检测到中文字符，默认选择英文语言`, 'info');
                        }
                    }
                    
                    return processedOrder;
                });
                
            } catch (error) {
                this.logger?.logError('多订单中文语言自动检测失败', error);
                return orders;
            }
        }

        /**
         * 获取检测统计信息
         * @param {Array} orders - 订单数组
         * @returns {Object} 统计信息
         */
        getDetectionStats(orders) {
            if (!Array.isArray(orders)) return null;
            
            let chineseCount = 0;
            let englishCount = 0;
            let unknownCount = 0;
            
            orders.forEach(order => {
                const orderText = order.rawText || order.customerName || '';
                const detection = this.detectLanguageInfo(orderText);
                
                if (detection.hasChinese) {
                    chineseCount++;
                } else if (orderText.trim()) {
                    englishCount++;
                } else {
                    unknownCount++;
                }
            });
            
            return {
                total: orders.length,
                chinese: chineseCount,
                english: englishCount,
                unknown: unknownCount,
                chinesePercentage: Math.round((chineseCount / orders.length) * 100),
                englishPercentage: Math.round((englishCount / orders.length) * 100)
            };
        }
    }

    // 创建单例实例
    const chineseLanguageDetector = new ChineseLanguageDetector();

    // 导出到全局命名空间
    window.OTA.ChineseLanguageDetector = ChineseLanguageDetector;
    window.OTA.chineseLanguageDetector = chineseLanguageDetector;

    // 向后兼容的全局访问
    window.ChineseLanguageDetector = ChineseLanguageDetector;
    window.chineseLanguageDetector = chineseLanguageDetector;

    // 工厂函数
    window.getChineseLanguageDetector = function() {
        if (!chineseLanguageDetector.logger) {
            chineseLanguageDetector.init();
        }
        return chineseLanguageDetector;
    };

    // 自动初始化
    if (typeof getLogger === 'function') {
        chineseLanguageDetector.init();
    } else {
        // 延迟初始化，等待logger可用
        document.addEventListener('DOMContentLoaded', () => {
            if (typeof getLogger === 'function') {
                chineseLanguageDetector.init();
            }
        });
    }

    getLogger()?.log('中文语言检测工具类已加载', 'info');

})();