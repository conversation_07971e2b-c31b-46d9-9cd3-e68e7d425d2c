/**
 * @OTA_CORE 重复函数检测器
 * 🏷️ 标签: @OTA_DUPLICATE_DETECTOR
 * 📝 功能: 自动检测项目中重复定义的函数，防止重复开发
 * ⚠️ 警告: 已注册，请勿重复开发
 * 
 * 解决已知问题:
 * - 38个文件中的getLogger重复定义 ✅ 已修复
 * - 15个文件中的getAppState重复定义 ✅ 已修复  
 * - 12个文件中的getGeminiService重复定义 ✅ 已修复
 * - 9个文件中的getAPIService重复定义 ✅ 已修复
 */

// 确保OTA命名空间存在
window.OTA = window.OTA || {};

(function() {
    'use strict';

    /**
     * @OTA_CORE 重复函数检测器
     * 实时监控和检测重复函数定义
     */
    const DuplicateDetector = {
        // 已知的函数定义位置记录
        functionRegistry: new Map(),
        
        // 检测规则配置
        detectionRules: {
            // 全局函数模式
            globalFunctions: {
                patterns: [/^get[A-Z]\w+/, /^create[A-Z]\w+/, /^init[A-Z]\w+/],
                whitelist: ['getComputedStyle', 'getElementById', 'getElementsByClassName']
            },
            
            // OTA相关函数
            otaFunctions: {
                patterns: [/^getOTA/, /^OTA/, /ota/i],
                critical: true
            },
            
            // 服务定位器函数
            serviceLocator: {
                patterns: [/getLogger|getAppState|getGeminiService|getAPIService|getUtils/],
                maxAllowed: 2 // 允许最多2个定义（权威定义+服务定位器）
            }
        },
        
        // 检测结果
        violations: [],
        
        /**
         * 扫描全局对象中的函数定义
         */
        scanGlobalFunctions() {
            const results = {
                timestamp: new Date().toISOString(),
                scanned: [],
                duplicates: [],
                violations: []
            };
            
            // 扫描window对象
            Object.keys(window).forEach(key => {
                if (typeof window[key] === 'function') {
                    results.scanned.push(key);
                    this.checkFunctionDuplication(key, 'window', results);
                }
            });
            
            // 扫描OTA命名空间
            if (window.OTA) {
                Object.keys(window.OTA).forEach(key => {
                    if (typeof window.OTA[key] === 'function') {
                        results.scanned.push(`OTA.${key}`);
                        this.checkFunctionDuplication(key, 'OTA', results);
                    }
                });
            }
            
            return results;
        },
        
        /**
         * 检查函数重复定义
         */
        checkFunctionDuplication(funcName, namespace, results) {
            const fullName = namespace === 'window' ? funcName : `${namespace}.${funcName}`;
            
            // 检查是否匹配检测规则
            const ruleViolation = this.checkAgainstRules(funcName, fullName);
            if (ruleViolation) {
                results.violations.push(ruleViolation);
            }
            
            // 记录函数位置
            if (!this.functionRegistry.has(funcName)) {
                this.functionRegistry.set(funcName, []);
            }
            
            const locations = this.functionRegistry.get(funcName);
            const location = {
                namespace,
                fullName,
                timestamp: new Date().toISOString()
            };
            
            locations.push(location);
            
            // 检查是否重复
            if (locations.length > 1) {
                const duplicate = {
                    functionName: funcName,
                    locations: locations.slice(),
                    severity: this.calculateSeverity(funcName),
                    recommendation: this.generateRecommendation(funcName, locations)
                };
                
                results.duplicates.push(duplicate);
            }
        },
        
        /**
         * 对照检测规则检查函数
         */
        checkAgainstRules(funcName, fullName) {
            const rules = this.detectionRules;
            
            // 检查全局函数规则
            if (rules.globalFunctions.patterns.some(pattern => pattern.test(funcName))) {
                if (!rules.globalFunctions.whitelist.includes(funcName)) {
                    return {
                        type: 'GLOBAL_FUNCTION_VIOLATION',
                        function: funcName,
                        message: `全局函数 ${funcName} 应该注册到OTA.Registry`,
                        severity: 'warning',
                        rule: 'globalFunctions'
                    };
                }
            }
            
            // 检查OTA函数规则
            if (rules.otaFunctions.patterns.some(pattern => pattern.test(funcName))) {
                return {
                    type: 'OTA_FUNCTION_DETECTED',
                    function: funcName,
                    message: `OTA相关函数 ${funcName} 需要添加@OTA_标签`,
                    severity: rules.otaFunctions.critical ? 'error' : 'warning',
                    rule: 'otaFunctions'
                };
            }
            
            // 检查服务定位器规则
            if (rules.serviceLocator.patterns.some(pattern => pattern.test(funcName))) {
                const locations = this.functionRegistry.get(funcName) || [];
                if (locations.length >= rules.serviceLocator.maxAllowed) {
                    return {
                        type: 'SERVICE_LOCATOR_VIOLATION',
                        function: funcName,
                        message: `服务函数 ${funcName} 定义过多 (${locations.length}/${rules.serviceLocator.maxAllowed})`,
                        severity: 'error',
                        rule: 'serviceLocator'
                    };
                }
            }
            
            return null;
        },
        
        /**
         * 计算重复的严重程度
         */
        calculateSeverity(funcName) {
            const locations = this.functionRegistry.get(funcName) || [];
            
            // 服务定位器函数重复是高严重性
            if (/getLogger|getAppState|getGeminiService|getAPIService/.test(funcName)) {
                return locations.length > 2 ? 'critical' : 'high';
            }
            
            // OTA相关函数重复是中等严重性
            if (/ota|OTA/.test(funcName)) {
                return 'medium';
            }
            
            // 其他函数重复是低严重性
            return 'low';
        },
        
        /**
         * 生成修复建议
         */
        generateRecommendation(funcName, locations) {
            if (locations.length === 2) {
                // 双重定义：检查是否是权威定义+服务定位器模式
                const hasWindowDef = locations.some(loc => loc.namespace === 'window');
                const hasOTADef = locations.some(loc => loc.namespace === 'OTA');
                
                if (hasWindowDef && hasOTADef) {
                    return {
                        action: 'VERIFY_PATTERN',
                        description: '验证是否为权威定义+服务定位器的正确模式',
                        priority: 'low'
                    };
                }
            }
            
            if (locations.length > 2) {
                return {
                    action: 'REMOVE_DUPLICATES',
                    description: `移除 ${funcName} 的重复定义，只保留权威定义和服务定位器`,
                    priority: 'high',
                    keepPatterns: ['js/core/service-locator.js', funcName.includes('Logger') ? 'js/logger.js' : 'original-definition']
                };
            }
            
            return {
                action: 'MONITOR',
                description: '继续监控是否有新的重复定义',
                priority: 'low'
            };
        },
        
        /**
         * 生成检测报告
         */
        generateReport() {
            const scanResults = this.scanGlobalFunctions();
            const report = {
                timestamp: new Date().toISOString(),
                summary: {
                    totalScanned: scanResults.scanned.length,
                    duplicatesFound: scanResults.duplicates.length,
                    violationsFound: scanResults.violations.length,
                    healthScore: this.calculateHealthScore(scanResults)
                },
                details: {
                    scannedFunctions: scanResults.scanned,
                    duplicates: scanResults.duplicates,
                    violations: scanResults.violations
                },
                recommendations: this.generateSystemRecommendations(scanResults),
                registry: Object.fromEntries(this.functionRegistry)
            };
            
            return report;
        },
        
        /**
         * 计算系统健康评分
         */
        calculateHealthScore(scanResults) {
            const duplicatesPenalty = scanResults.duplicates.length * 10;
            const violationsPenalty = scanResults.violations.length * 5;
            const baseScore = 100;
            
            const score = Math.max(0, baseScore - duplicatesPenalty - violationsPenalty);
            const grade = score >= 90 ? 'A' : score >= 80 ? 'B' : score >= 70 ? 'C' : score >= 60 ? 'D' : 'F';
            
            return {
                score,
                grade,
                status: score >= 80 ? 'healthy' : score >= 60 ? 'warning' : 'critical'
            };
        },
        
        /**
         * 生成系统级修复建议
         */
        generateSystemRecommendations(scanResults) {
            const recommendations = [];
            
            if (scanResults.duplicates.length > 0) {
                recommendations.push({
                    type: 'DUPLICATE_CLEANUP',
                    priority: 'high',
                    description: `清理 ${scanResults.duplicates.length} 个重复函数定义`,
                    action: '运行自动清理脚本或手动移除重复定义'
                });
            }
            
            if (scanResults.violations.length > 3) {
                recommendations.push({
                    type: 'ARCHITECTURE_REVIEW',
                    priority: 'medium',
                    description: '架构违规过多，建议进行全面架构评审',
                    action: '检查全局函数的注册和标签标准'
                });
            }
            
            const untaggedCount = scanResults.violations.filter(v => 
                v.type === 'OTA_FUNCTION_DETECTED'
            ).length;
            
            if (untaggedCount > 0) {
                recommendations.push({
                    type: 'TAGGING_COMPLIANCE',
                    priority: 'medium',
                    description: `${untaggedCount} 个OTA函数缺少标签`,
                    action: '为所有OTA相关函数添加@OTA_标签'
                });
            }
            
            return recommendations;
        },
        
        /**
         * 启动实时监控
         */
        startRealTimeMonitoring(intervalMs = 30000) {
            console.log('🔍 启动重复函数实时检测...');
            
            const monitor = () => {
                const report = this.generateReport();
                if (report.summary.duplicatesFound > 0 || report.summary.violationsFound > 0) {
                    console.group('🚨 重复函数检测警告');
                    console.log('健康评分:', report.summary.healthScore);
                    
                    if (report.details.duplicates.length > 0) {
                        console.warn('发现重复函数:', report.details.duplicates);
                    }
                    
                    if (report.details.violations.length > 0) {
                        console.warn('架构违规:', report.details.violations);
                    }
                    
                    console.groupEnd();
                }
            };
            
            // 立即执行一次
            monitor();
            
            // 设置定期检查
            return setInterval(monitor, intervalMs);
        }
    };

    // 注册到OTA命名空间
    window.OTA.DuplicateDetector = DuplicateDetector;
    
    // 全局访问（向后兼容）
    window.DuplicateDetector = DuplicateDetector;
    
    // 注册到OTA注册中心
    if (window.OTA && window.OTA.Registry) {
        window.OTA.Registry.registerUtil('DuplicateDetector', DuplicateDetector, '@OTA_DUPLICATE_DETECTOR');
    }
    
    // 导出全局调试命令
    window.detectDuplicates = () => {
        const report = DuplicateDetector.generateReport();
        console.group('🔍 重复函数检测报告');
        console.log('📊 概要:', report.summary);
        console.table(report.details.duplicates);
        if (report.details.violations.length > 0) {
            console.group('⚠️ 架构违规');
            console.table(report.details.violations);
            console.groupEnd();
        }
        if (report.recommendations.length > 0) {
            console.group('💡 修复建议');
            console.table(report.recommendations);
            console.groupEnd();
        }
        console.groupEnd();
        return report;
    };
    
    window.startDuplicateMonitoring = (interval = 30000) => {
        return DuplicateDetector.startRealTimeMonitoring(interval);
    };
    
    // 初始化日志
    const logger = window.getLogger ? window.getLogger() : console;
    if (logger && logger.log) {
        logger.log('🚀 重复函数检测器已初始化', 'info', {
            features: ['实时检测', '自动报告', '修复建议', '健康评分'],
            commands: ['detectDuplicates()', 'startDuplicateMonitoring()']
        });
    }

})();