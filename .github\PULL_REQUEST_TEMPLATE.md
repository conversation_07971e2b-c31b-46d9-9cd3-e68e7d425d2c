# OTA订单处理系统 - 代码审查检查清单

## 📋 基本信息
- **PR类型**: [ ] 新功能 [ ] Bug修复 [ ] 重构 [ ] 文档更新 [ ] 其他: _______
- **影响范围**: [ ] 前端UI [ ] 业务逻辑 [ ] API集成 [ ] 数据处理 [ ] 配置文件
- **测试状态**: [ ] 已测试 [ ] 需要测试 [ ] 测试用例已更新

## 🚨 防重复开发检查

### 全局函数检查
- [ ] **没有重复定义**：确认没有重复定义getLogger, getAppState, getGeminiService等函数
- [ ] **使用服务定位器**：新的依赖获取使用`getService('serviceName')`模式
- [ ] **避免直接全局注册**：避免直接向window对象添加新的全局函数
- [ ] **运行重复检测**：执行`detectDuplicates()`确认无重复

### OTA标签规范检查
- [ ] **@OTA_SERVICE**: 所有服务类已添加@OTA_SERVICE标签
- [ ] **@OTA_MANAGER**: 所有管理器类已添加@OTA_MANAGER标签  
- [ ] **@OTA_FACTORY**: 所有工厂函数已添加@OTA_FACTORY标签
- [ ] **@OTA_UTIL**: 所有工具函数已添加@OTA_UTIL标签
- [ ] **@OTA_CORE**: 核心架构文件已添加@OTA_CORE标签

## 🏗️ 架构规范检查

### 依赖管理
- [ ] **统一服务获取**：使用`this.logger = getLogger()`模式缓存服务实例
- [ ] **避免重复获取**：避免在方法中重复调用`getLogger()`等
- [ ] **注册到Registry**：新的服务已注册到OTA.Registry
- [ ] **命名规范**：函数命名遵循驼峰命名法和业务语义

### 代码质量
- [ ] **无过度工程化**：避免创建不必要的抽象层或复杂配置
- [ ] **功能聚焦**：每个模块专注于单一职责
- [ ] **配置提取**：重复的配置已提取为通用对象
- [ ] **错误处理**：适当的错误处理和日志记录

## 📝 开发质量检查

### 代码风格
- [ ] **注释适度**：有必要的技术文档，但避免过度详细的注释
- [ ] **日志合理**：使用this.logger模式，避免过度日志记录
- [ ] **调试代码清理**：已移除console.log和测试代码
- [ ] **格式一致**：代码格式和缩进一致

### 文件组织
- [ ] **文件位置合理**：新文件放在合适的目录结构中
- [ ] **命名清晰**：文件名和类名清晰反映其功能
- [ ] **导入导出规范**：遵循项目的模块导入导出模式
- [ ] **避免大文件**：单个文件不超过1000行（特殊情况需说明）

## 🧪 测试和验证

### 功能测试
- [ ] **核心功能**：登录、订单解析、多订单处理、订单创建功能正常
- [ ] **边界情况**：测试了异常输入和错误情况
- [ ] **兼容性**：确保不破坏现有功能
- [ ] **性能**：没有显著的性能下降

### 架构测试
- [ ] **重复检测通过**：运行`detectDuplicates()`无重复报告
- [ ] **Registry验证**：运行`otaRegistryReport()`确认注册正确
- [ ] **健康检查**：运行`performSystemHealthCheck()`通过
- [ ] **服务定位**：所有服务可通过getService()正确获取

## 📊 性能和资源

### 资源使用
- [ ] **内存效率**：没有内存泄漏或过度内存使用
- [ ] **文件大小**：新增代码量合理，避免冗余
- [ ] **加载性能**：不影响页面加载速度
- [ ] **执行效率**：避免不必要的计算和DOM操作

### 监控和日志
- [ ] **关键操作日志**：重要操作有适当的日志记录
- [ ] **错误监控**：错误能被正确捕获和报告
- [ ] **性能指标**：关键操作有性能监控
- [ ] **调试友好**：便于问题诊断和调试

## 📚 文档和沟通

### 代码文档
- [ ] **JSDoc完整**：复杂函数有JSDoc注释
- [ ] **README更新**：如果需要，已更新README
- [ ] **CLAUDE.md更新**：如果有架构变更，已更新项目文档
- [ ] **变更说明**：在PR描述中清楚说明了变更内容

### 团队协作
- [ ] **破坏性变更说明**：如有API变更，已详细说明影响
- [ ] **迁移指南**：如需要，提供了迁移指南
- [ ] **向后兼容**：尽可能保持向后兼容
- [ ] **沟通充分**：与相关人员充分沟通了变更

## ✅ 检查命令

在提交PR前，请运行以下命令进行自检：

```bash
# 检查重复函数定义
detectDuplicates()

# 检查OTA注册状态  
otaRegistryReport()

# 系统健康检查
performSystemHealthCheck()

# 检查架构违规
checkDuplicates()
```

## 🔍 审查者检查清单

**审查者请确认以下项目：**

- [ ] **代码审查**：仔细审查了代码逻辑和实现
- [ ] **架构合规**：确认遵循了OTA项目架构规范
- [ ] **测试验证**：验证了功能和性能测试结果
- [ ] **文档检查**：确认文档更新完整准确
- [ ] **重复检测**：运行了重复检测工具并确认通过
- [ ] **风险评估**：评估了潜在风险和影响

## 📋 最终确认

- [ ] 我已仔细阅读并遵循了所有检查项目
- [ ] 我已运行了所有必要的检查命令
- [ ] 我确认这个PR不会引入重复开发问题
- [ ] 我确认这个PR符合OTA项目的架构标准

**签名**: _____________ **日期**: _____________