/**
 * 多选下拉菜单组件
 * 支持复选框的下拉菜单，符合Fluent Design风格
 * <AUTHOR>
 * @version 1.0.0
 */

// 获取依赖模块 - 使用统一的服务定位器

class MultiSelectDropdown {
    constructor(containerId, options = {}) {
        this.containerId = containerId;
        this.container = document.getElementById(containerId);
        this.trigger = null;
        this.optionsContainer = null;
        this.hiddenSelect = null;
        this.selectedValues = new Set();
        this.isOpen = false;
        
        // 配置选项
        this.options = {
            placeholder: '请选择语言',
            maxDisplayItems: 3,
            searchable: false,
            ...options
        };
        
        this.init();
    }

    /**
     * 初始化多选下拉菜单
     */
    init() {
        if (!this.container) {
            const logger = getLogger();
            if (logger) {
                logger.log(`多选下拉菜单容器 ${this.containerId} 未找到`, 'warning');
            }
            return;
        }

        this.trigger = this.container.querySelector('.multi-select-trigger');
        this.optionsContainer = this.container.querySelector('.multi-select-options');
        this.hiddenSelect = this.container.querySelector('select');
        
        if (!this.trigger || !this.optionsContainer || !this.hiddenSelect) {
            const logger = getLogger();
            if (logger) {
                logger.log('多选下拉菜单必要元素缺失', 'error');
            }
            return;
        }

        this.setupEventListeners();
        this.loadOptions();
        
        const logger = getLogger();
        if (logger) {
            logger.log(`多选下拉菜单 ${this.containerId} 已初始化`, 'info');
        }
    }

    /**
     * 设置事件监听器
     */
    setupEventListeners() {
        // 点击触发器展开/收起
        this.trigger.addEventListener('click', (e) => {
            e.preventDefault();
            e.stopPropagation();
            this.toggle();
        });

        // 键盘导航支持
        this.trigger.addEventListener('keydown', (e) => {
            if (e.key === 'Enter' || e.key === ' ') {
                e.preventDefault();
                this.toggle();
            } else if (e.key === 'Escape') {
                this.close();
            }
        });

        // 点击外部区域关闭
        document.addEventListener('click', (e) => {
            if (!this.container.contains(e.target)) {
                this.close();
            }
        });

        // 阻止选项容器的点击事件冒泡
        this.optionsContainer.addEventListener('click', (e) => {
            e.stopPropagation();
        });
    }

    /**
     * 加载选项数据
     */
    loadOptions() {
        // 从隐藏的select元素获取选项
        const options = Array.from(this.hiddenSelect.options);
        this.optionsContainer.innerHTML = '';

        options.forEach((option, index) => {
            if (option.value === '') return; // 跳过占位符选项

            const optionElement = this.createOptionElement(option.value, option.textContent, index);
            this.optionsContainer.appendChild(optionElement);
        });
    }

    /**
     * 创建选项元素
     */
    createOptionElement(value, text, index) {
        const optionDiv = document.createElement('div');
        optionDiv.className = 'multi-select-option';
        optionDiv.setAttribute('role', 'option');
        optionDiv.setAttribute('tabindex', '0');
        optionDiv.setAttribute('data-value', value);

        const checkbox = document.createElement('input');
        checkbox.type = 'checkbox';
        checkbox.className = 'multi-select-checkbox';
        checkbox.id = `${this.containerId}_option_${index}`;
        checkbox.value = value;

        const label = document.createElement('label');
        label.className = 'multi-select-label';
        label.setAttribute('for', checkbox.id);
        label.textContent = text;

        // 点击选项切换选中状态
        optionDiv.addEventListener('click', (e) => {
            e.preventDefault();
            checkbox.checked = !checkbox.checked;
            this.updateSelection(value, checkbox.checked);
        });

        // 键盘导航
        optionDiv.addEventListener('keydown', (e) => {
            if (e.key === 'Enter' || e.key === ' ') {
                e.preventDefault();
                checkbox.checked = !checkbox.checked;
                this.updateSelection(value, checkbox.checked);
            }
        });

        optionDiv.appendChild(checkbox);
        optionDiv.appendChild(label);

        return optionDiv;
    }

    /**
     * 更新选择状态
     */
    updateSelection(value, isSelected) {
        if (isSelected) {
            this.selectedValues.add(value);
        } else {
            this.selectedValues.delete(value);
        }

        this.updateHiddenSelect();
        this.updateDisplayText();
        this.updateAriaAttributes();

        // 触发change事件
        const changeEvent = new Event('change', { bubbles: true });
        this.hiddenSelect.dispatchEvent(changeEvent);
    }

    /**
     * 更新隐藏的select元素
     */
    updateHiddenSelect() {
        Array.from(this.hiddenSelect.options).forEach(option => {
            option.selected = this.selectedValues.has(option.value);
        });
    }

    /**
     * 更新显示文本
     */
    updateDisplayText() {
        const textElement = this.trigger.querySelector('.multi-select-text');
        
        if (this.selectedValues.size === 0) {
            textElement.textContent = this.options.placeholder;
            textElement.classList.add('placeholder');
        } else {
            const selectedTexts = Array.from(this.selectedValues).map(value => {
                const option = this.hiddenSelect.querySelector(`option[value="${value}"]`);
                return option ? option.textContent : value;
            });

            if (selectedTexts.length <= this.options.maxDisplayItems) {
                textElement.textContent = selectedTexts.join(', ');
            } else {
                textElement.textContent = `已选择 ${selectedTexts.length} 项`;
            }
            textElement.classList.remove('placeholder');
        }
    }

    /**
     * 更新ARIA属性
     */
    updateAriaAttributes() {
        const selectedCount = this.selectedValues.size;
        this.trigger.setAttribute('aria-label', 
            selectedCount > 0 ? `已选择 ${selectedCount} 个语言` : '请选择语言'
        );
    }

    /**
     * 展开/收起下拉菜单
     */
    toggle() {
        if (this.isOpen) {
            this.close();
        } else {
            this.open();
        }
    }

    /**
     * 展开下拉菜单
     */
    open() {
        this.isOpen = true;
        this.trigger.setAttribute('aria-expanded', 'true');

        // 计算浮窗位置
        this.updateFloatingPosition();

        this.optionsContainer.classList.add('show');

        // 聚焦第一个选项
        const firstOption = this.optionsContainer.querySelector('.multi-select-option');
        if (firstOption) {
            firstOption.focus();
        }

        // 监听页面滚动和窗口大小变化
        this.addScrollListeners();
    }

    /**
     * 收起下拉菜单
     */
    close() {
        this.isOpen = false;
        this.trigger.setAttribute('aria-expanded', 'false');
        this.optionsContainer.classList.remove('show');

        // 移除滚动监听
        this.removeScrollListeners();
    }

    /**
     * 更新浮窗位置
     */
    updateFloatingPosition() {
        if (!this.trigger || !this.optionsContainer) return;

        const triggerRect = this.trigger.getBoundingClientRect();
        const viewportHeight = window.innerHeight;
        const viewportWidth = window.innerWidth;
        const optionsHeight = 250; // 最大高度

        // 计算最佳显示位置
        let top = triggerRect.bottom + 4; // 默认显示在下方
        let left = triggerRect.left;
        let width = triggerRect.width;

        // 检查是否超出视口底部
        if (top + optionsHeight > viewportHeight) {
            // 显示在上方
            top = triggerRect.top - optionsHeight - 4;
        }

        // 检查是否超出视口右侧
        if (left + width > viewportWidth) {
            left = viewportWidth - width - 10;
        }

        // 检查是否超出视口左侧
        if (left < 10) {
            left = 10;
            width = Math.min(width, viewportWidth - 20);
        }

        // 应用位置样式
        this.optionsContainer.style.top = `${Math.max(10, top)}px`;
        this.optionsContainer.style.left = `${left}px`;
        this.optionsContainer.style.width = `${width}px`;
    }

    /**
     * 添加滚动监听器
     */
    addScrollListeners() {
        this.scrollHandler = () => {
            if (this.isOpen) {
                this.updateFloatingPosition();
            }
        };

        this.resizeHandler = () => {
            if (this.isOpen) {
                this.updateFloatingPosition();
            }
        };

        window.addEventListener('scroll', this.scrollHandler, { passive: true });
        window.addEventListener('resize', this.resizeHandler, { passive: true });
    }

    /**
     * 移除滚动监听器
     */
    removeScrollListeners() {
        if (this.scrollHandler) {
            window.removeEventListener('scroll', this.scrollHandler);
        }
        if (this.resizeHandler) {
            window.removeEventListener('resize', this.resizeHandler);
        }
    }

    /**
     * 设置选中值
     */
    setSelectedValues(values) {
        this.selectedValues.clear();
        values.forEach(value => this.selectedValues.add(value));
        
        // 更新复选框状态
        this.optionsContainer.querySelectorAll('.multi-select-checkbox').forEach(checkbox => {
            checkbox.checked = this.selectedValues.has(checkbox.value);
        });
        
        this.updateHiddenSelect();
        this.updateDisplayText();
        this.updateAriaAttributes();
    }

    /**
     * 获取选中值
     */
    getSelectedValues() {
        return Array.from(this.selectedValues);
    }

    /**
     * 清空选择
     */
    clearSelection() {
        this.setSelectedValues([]);
    }

    /**
     * 销毁组件
     */
    destroy() {
        // 关闭下拉菜单
        this.close();

        // 移除事件监听器
        this.removeScrollListeners();

        const logger = getLogger();
        if (logger) {
            logger.log(`多选下拉菜单 ${this.containerId} 已销毁`, 'info');
        }
    }
}

// 导出到全局命名空间
if (typeof window !== 'undefined') {
    if (!window.OTA) {
        window.OTA = {};
    }
    window.OTA.MultiSelectDropdown = MultiSelectDropdown;
    
    // 向后兼容
    window.MultiSelectDropdown = MultiSelectDropdown;
}
