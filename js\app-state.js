/**
 * @OTA_SERVICE 应用状态管理模块
 * 🏷️ 标签: @OTA_APP_STATE_SERVICE
 * 📝 说明: 负责管理应用的全局状态，包括用户认证、数据缓存、配置等
 * ⚠️ 警告: 已注册，请勿重复开发
 * 重构为传统script标签加载方式
 */

// 确保OTA命名空间存在
window.OTA = window.OTA || {};

(function() {
    'use strict';

    // 获取依赖模块 - 使用统一的服务定位器

class AppState {
    constructor() {
        // 步骤 1: 优先初始化核心属性，解决 a is not defined of auth 问题
        this.state = {
            auth: {
                isLoggedIn: false,
                token: null,
                user: null,
                tokenExpiry: null
            },
            
            // 系统数据缓存
            systemData: {
                backendUsers: [],
                subCategories: [],
                carTypes: [],
                drivingRegions: [],
                languages: [],
                lastUpdated: null
            },
            
            // 当前订单数据
            currentOrder: {
                rawInput: '',
                parsedData: {},
                formData: {},
                validationErrors: [],
                status: 'draft' // draft, parsing, validated, creating, created, error
            },
            
            // 应用配置
            config: {
                theme: 'light',
                language: 'en', // 添加语言配置，默认英文
                debugMode: false,
                autoSave: true,
                defaultBackendUserId: null,
                geminiApiKey: null
            },
            
            // 系统状态
            system: {
                connected: false,
                lastApiCall: null,
                apiCallCount: 0,
                errors: []
            }
        };
        this.listeners = new Map();

        // 从本地存储加载状态（包括认证信息）
        // loadFromStorage()方法内部会自动检查token是否过期并处理
        this.loadFromStorage();
    }
    
    /**
     * 从本地存储加载状态
     */
    loadFromStorage() {
        try {
            const saved = localStorage.getItem('ota-system-state');
            if (saved) {
                const parsed = JSON.parse(saved);
                this.state = { ...this.state, ...parsed };
                
                // 验证token是否过期
                if (this.state.auth.tokenExpiry && 
                    new Date(this.state.auth.tokenExpiry) <= new Date()) {
                    // **修复**: 清除过期的认证，但保留rememberMe的偏好
                    this.clearAuth(this.state.auth.rememberMe);
                }
                
                // 验证系统数据是否过期（24小时）
                if (this.state.systemData.lastUpdated) {
                    const lastUpdate = new Date(this.state.systemData.lastUpdated);
                    const now = new Date();
                    const hoursDiff = (now - lastUpdate) / (1000 * 60 * 60);
                    
                    if (hoursDiff > 24) {
                        this.clearSystemData();
                    }
                }
            }
            
            // 加载主题设置
            const savedTheme = localStorage.getItem('ota_theme_preference');
            if (savedTheme && (savedTheme === 'light' || savedTheme === 'dark')) {
                this.state.config.theme = savedTheme;
                document.documentElement.setAttribute('data-theme', savedTheme);
            }
            
            // 加载语言设置
            const savedLanguage = localStorage.getItem('ota_language_preference');
            if (savedLanguage && (savedLanguage === 'zh' || savedLanguage === 'en')) {
                this.state.config.language = savedLanguage;
            }
            
        } catch (error) {
            console.error('加载状态失败:', error);
            this.clearStorage();
        }
    }
    
    /**
     * 保存状态到本地存储
     */
    saveToStorage() {
        try {
            localStorage.setItem('ota-system-state', JSON.stringify(this.state));
        } catch (error) {
            console.error('保存状态失败:', error);
        }
    }
    
    /**
     * 清空本地存储
     */
    clearStorage() {
        localStorage.removeItem('ota-system-state');
    }
    
    /**
     * 获取状态值
     * @param {string} path - 状态路径，如 'auth.isLoggedIn'
     * @returns {any} 状态值
     */
    get(path) {
        return path.split('.').reduce((obj, key) => obj?.[key], this.state);
    }
    
    /**
     * 设置状态值
     * @param {string} path - 状态路径
     * @param {any} value - 新值
     * @param {boolean} save - 是否保存到本地存储
     */
    set(path, value, save = true) {
        const keys = path.split('.');
        const lastKey = keys.pop();
        const target = keys.reduce((obj, key) => {
            if (!obj[key]) obj[key] = {};
            return obj[key];
        }, this.state);
        
        const oldValue = target[lastKey];
        target[lastKey] = value;
        
        if (save) {
            this.saveToStorage();
        }
        
        // 触发监听器
        this.notify(path, value, oldValue);
    }
    
    /**
     * 更新状态（合并对象）
     * @param {string} path - 状态路径
     * @param {object} updates - 更新的对象
     * @param {boolean} save - 是否保存到本地存储
     */
    update(path, updates, save = true) {
        const current = this.get(path) || {};
        const newValue = { ...current, ...updates };
        this.set(path, newValue, save);
    }
    
    /**
     * 添加状态监听器
     * @param {string} path - 监听的状态路径
     * @param {function} callback - 回调函数
     */
    on(path, callback) {
        if (!this.listeners.has(path)) {
            this.listeners.set(path, []);
        }
        this.listeners.get(path).push(callback);
    }
    
    /**
     * 移除状态监听器
     * @param {string} path - 状态路径
     * @param {function} callback - 回调函数
     */
    off(path, callback) {
        const callbacks = this.listeners.get(path);
        if (callbacks) {
            const index = callbacks.indexOf(callback);
            if (index > -1) {
                callbacks.splice(index, 1);
            }
        }
    }
    
    /**
     * 通知监听器
     * @param {string} path - 状态路径
     * @param {any} newValue - 新值
     * @param {any} oldValue - 旧值
     */
    notify(path, newValue, oldValue) {
        // 通知精确路径的监听器
        const callbacks = this.listeners.get(path);
        if (callbacks) {
            callbacks.forEach(callback => {
                try {
                    callback(newValue, oldValue, path);
                } catch (error) {
                    console.error('状态监听器错误:', error);
                }
            });
        }

        // 如果更新的是对象，也要通知子路径的监听器
        if (typeof newValue === 'object' && newValue !== null) {
            Object.keys(newValue).forEach(key => {
                const subPath = `${path}.${key}`;
                const subCallbacks = this.listeners.get(subPath);
                if (subCallbacks) {
                    const subNewValue = newValue[key];
                    const subOldValue = oldValue && typeof oldValue === 'object' ? oldValue[key] : undefined;
                    subCallbacks.forEach(callback => {
                        try {
                            callback(subNewValue, subOldValue, subPath);
                        } catch (error) {
                            console.error('状态监听器错误:', error);
                        }
                    });
                }
            });
        }
    }
    
    /**
     * 设置认证信息
     * @param {string} token - 认证token
     * @param {object} user - 用户信息
     * @param {boolean} rememberMe - 是否保持登录状态
     */
    setAuth(token, user = null, rememberMe = false) {
        const tokenExpiry = new Date();
        
        // 根据"保持登录"设置不同的过期时间
        if (rememberMe) {
            tokenExpiry.setDate(tokenExpiry.getDate() + 30); // 30天过期
        } else {
            tokenExpiry.setHours(tokenExpiry.getHours() + 12); // 12小时过期
        }
        
        this.update('auth', {
            isLoggedIn: true,
            token,
            user,
            tokenExpiry: tokenExpiry.toISOString(),
            rememberMe
        });
        
        this.set('system.connected', true);
    }
    
    /**
     * 清除认证信息
     * @param {boolean} keepRememberMe - 是否保持记住登录状态
     */
    clearAuth(keepRememberMe = false) {
        const currentRememberMe = keepRememberMe ? this.get('auth.rememberMe') : false;
        
        this.update('auth', {
            isLoggedIn: false,
            token: null,
            user: null,
            tokenExpiry: null,
            rememberMe: currentRememberMe
        });
        
        this.set('system.connected', false);
    }
    
    /**
     * 设置系统数据
     * @param {object} data - 系统数据
     */
    setSystemData(data) {
        this.update('systemData', {
            ...data,
            lastUpdated: new Date().toISOString()
        });
    }
    
    /**
     * 清除系统数据
     */
    clearSystemData() {
        this.set('systemData', {
            backendUsers: [],
            subCategories: [],
            carTypes: [],
            drivingRegions: [],
            languages: [],
            lastUpdated: null
        });
    }
    
    /**
     * 设置默认后台用户ID
     * @param {number} userId - 用户ID
     */
    setDefaultBackendUser(userId) {
        this.set('config.defaultBackendUserId', userId);
    }
    
    /**
     * 获取默认后台用户ID
     * @returns {number|null} 用户ID
     */
    getDefaultBackendUser() {
        const defaultId = this.get('config.defaultBackendUserId');
        if (defaultId) return defaultId;
        
        // 自动选择第一个可用的后台用户
        const users = this.get('systemData.backendUsers');
        if (users && users.length > 0) {
            const firstUserId = users[0].id;
            this.setDefaultBackendUser(firstUserId);
            return firstUserId;
        }
        
        return null;
    }
    
    /**
     * 设置当前订单数据
     * @param {object} orderData - 订单数据
     */
    setCurrentOrder(orderData) {
        this.update('currentOrder', orderData);
    }
    
    /**
     * 清除当前订单
     */
    clearCurrentOrder() {
        this.set('currentOrder', {
            rawInput: '',
            parsedData: {},
            formData: {},
            validationErrors: [],
            status: 'draft'
        });
    }
    
    /**
     * 设置主题
     * @param {string} theme - 主题名称
     */
    setTheme(theme) {
        this.set('config.theme', theme);
        document.documentElement.setAttribute('data-theme', theme);
    }
    
    /**
     * 切换调试模式
     * @param {boolean} enabled - 是否启用
     */
    setDebugMode(enabled) {
        this.set('config.debugMode', enabled);
    }
    
    /**
     * 记录API调用
     * @param {object} callInfo - 调用信息
     */
    recordApiCall(callInfo) {
        this.set('system.lastApiCall', new Date().toISOString());
        this.set('system.apiCallCount', this.get('system.apiCallCount') + 1);
    }
    
    /**
     * 添加系统错误
     * @param {object} error - 错误信息
     */
    addError(error) {
        const errors = this.get('system.errors') || [];
        errors.push({
            ...error,
            timestamp: new Date().toISOString(),
            id: Date.now()
        });
        
        // 保持最新100个错误
        if (errors.length > 100) {
            errors.splice(0, errors.length - 100);
        }
        
        this.set('system.errors', errors);
    }
    
    /**
     * 清除系统错误
     */
    clearErrors() {
        this.set('system.errors', []);
    }
    
    /**
     * 导出状态（用于调试）
     * @returns {object} 状态对象
     */
    export() {
        return JSON.parse(JSON.stringify(this.state));
    }
    
    /**
     * 重置状态
     */
    reset() {
        this.clearStorage();
        this.state = {
            auth: { isLoggedIn: false, token: null, user: null, tokenExpiry: null },
            systemData: { backendUsers: [], subCategories: [], carTypes: [], 
                         drivingRegions: [], languages: [], lastUpdated: null },
            currentOrder: { rawInput: '', parsedData: {}, formData: {}, 
                          validationErrors: [], status: 'draft' },
            config: { theme: 'light', debugMode: false, autoSave: true, 
                     defaultBackendUserId: null, geminiApiKey: null },
            system: { connected: false, lastApiCall: null, apiCallCount: 0, errors: [] }
        };
    }
    }

    /**
     * @file AppState 账号相关静态配置扩展
     * @description 本地静态映射：账号 id -> ota 默认值及可选列表
     * <AUTHOR>
     * @date 2024-06-25
     */

    /**
     * 账号 id 到 ota 默认值和可选项的本地静态映射对象
     * 可根据实际需求扩展更多账号
     */
    const OTA_MAPPING = {
        2666: {
            default: "JR Coach Credit", // 默认 ota
            options: ["JR Coach Credit", "其他OTA1", "其他OTA2"] // 可选 ota 列表
        },
        // 示例：其他账号
        1234: {
            default: "Some OTA",
            options: ["Some OTA", "Another OTA"]
        }
        // ... 可继续扩展
    };

    /**
     * 获取指定用户 id 的 ota 配置（默认值和可选项）
     * @function getOtaConfigForUser
     * @param {number|string} userId - 当前登录用户的 id
     * @returns {{default: string, options: string[]}|null} 对应 ota 配置，未配置则返回 null
     */
    function getOtaConfigForUser(userId) {
        // 转为字符串以兼容不同 id 类型
        const key = String(userId);
        // 优先查找数字 key，再查字符串 key
        return OTA_MAPPING[userId] || OTA_MAPPING[key] || null;
    }

    // 创建全局状态实例（单例模式）
    let appStateInstance = null;

    /**
     * @OTA_FACTORY 获取应用状态管理器实例
     * 🏷️ 标签: @OTA_APP_STATE_FACTORY
     * 📝 说明: 单例工厂函数，获取应用状态管理器实例
     * ⚠️ 警告: 已注册，请勿重复开发
     * @returns {AppState} 状态管理器实例
     */
    function getAppState() {
        if (!appStateInstance) {
            appStateInstance = new AppState();
        }
        return appStateInstance;
    }

    // 创建默认实例以保持向后兼容性
    const appState = getAppState();

    // 暴露到OTA命名空间
    window.OTA.appState = appState;
    window.OTA.getAppState = getAppState;
    window.OTA.getOtaConfigForUser = getOtaConfigForUser;

    // 向后兼容：暴露到全局window对象
    window.appState = appState;
    window.getAppState = getAppState;
    
    // 注册到OTA注册中心
    if (window.OTA && window.OTA.Registry) {
        window.OTA.Registry.registerService('appState', appState, '@OTA_APP_STATE_SERVICE');
        window.OTA.Registry.registerFactory('getAppState', getAppState, '@OTA_APP_STATE_FACTORY');
        window.OTA.Registry.registerUtil('getOtaConfigForUser', getOtaConfigForUser, '@OTA_UTIL_OTA_CONFIG');
    }
    window.getOtaConfigForUser = getOtaConfigForUser;

})();