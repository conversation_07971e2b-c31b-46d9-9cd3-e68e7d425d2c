# OTA订单处理系统 - 编码规范

## 🎯 编码原则

### 核心价值观
1. **简单优于复杂** - 避免过度工程化
2. **一致性优于个性** - 遵循统一的编码风格
3. **可读性优于聪明** - 清晰的代码胜过炫技
4. **防重复优于重写** - 重用现有功能而非重新实现

## 🏗️ 架构规范

### 1. 服务架构模式

#### ✅ 正确的服务定义模式
```javascript
/**
 * @OTA_SERVICE 订单服务
 * 🏷️ 标签: @OTA_ORDER_SERVICE
 * 📝 功能: 处理订单相关业务逻辑
 * ⚠️ 警告: 已注册，请勿重复开发
 */
class OrderService {
    constructor() {
        // 缓存常用服务，避免重复获取
        this.logger = getLogger();
        this.appState = getAppState();
    }
    
    // 业务方法
    processOrder(orderData) {
        this.logger.log('开始处理订单', 'info');
        // 业务逻辑
    }
}

// 工厂函数
let orderServiceInstance = null;
function getOrderService() {
    if (!orderServiceInstance) {
        orderServiceInstance = new OrderService();
    }
    return orderServiceInstance;
}

// 注册到系统
window.OTA.OrderService = OrderService;
window.OTA.getOrderService = getOrderService;
window.getOrderService = getOrderService; // 向后兼容

// 注册到中央注册表
window.OTA.Registry.registerService('OrderService', OrderService, '@OTA_ORDER_SERVICE');
```

#### ❌ 错误的模式
```javascript
// ❌ 没有标签注释
class OrderService {
    constructor() {
        // ❌ 每次方法调用都获取服务
    }
    
    processOrder() {
        // ❌ 重复获取logger
        const logger = getLogger();
        logger.log('处理订单');
    }
}

// ❌ 直接污染全局，未注册
window.OrderService = OrderService;
```

### 2. 管理器模式

#### ✅ 正确的管理器模式
```javascript
/**
 * @OTA_MANAGER 订单管理器
 * 🏷️ 标签: @OTA_ORDER_MANAGER
 */
class OrderManager {
    constructor() {
        this.logger = getLogger();
        this.apiService = getAPIService();
        this.orders = new Map();
    }
    
    init() {
        this.setupEventListeners();
        this.loadInitialData();
    }
    
    cleanup() {
        // 清理资源，防止内存泄漏
        this.orders.clear();
        this.removeEventListeners();
    }
}
```

### 3. 工具函数模式

#### ✅ 正确的工具函数模式
```javascript
/**
 * @OTA_UTIL 日期工具
 * 🏷️ 标签: @OTA_DATE_UTILS
 */
const DateUtils = {
    formatDate(date) {
        // 实现
    },
    
    parseDate(dateStr) {
        // 实现
    }
};

// 注册
window.OTA.DateUtils = DateUtils;
window.OTA.Registry.registerUtil('DateUtils', DateUtils, '@OTA_DATE_UTILS');
```

## 🎨 代码风格规范

### 1. 命名规范

#### 变量和函数命名
```javascript
// ✅ 使用驼峰命名法
const orderManager = getOrderManager();
const customerInfo = getCustomerInfo();
const isOrderValid = validateOrder(order);

// ❌ 避免使用
const order_manager = getOrderManager();    // 下划线
const CustomerInfo = getCustomerInfo();     // 帕斯卡命名用于变量
const isordervalid = validateOrder(order);  // 全小写
```

#### 类和构造函数命名
```javascript
// ✅ 使用帕斯卡命名法
class OrderManager {}
class MultiOrderProcessor {}
class GeminiService {}

// ❌ 避免使用
class orderManager {}        // 小写开头
class multiOrder_processor {} // 下划线
```

#### 常量命名
```javascript
// ✅ 使用全大写+下划线
const MAX_ORDER_COUNT = 10;
const API_BASE_URL = 'https://api.example.com';
const DEFAULT_LANGUAGE = 'zh';

// ❌ 避免使用
const maxOrderCount = 10;    // 驼峰命名用于常量
const ApiBaseUrl = 'https://api.example.com'; // 帕斯卡命名用于常量
```

### 2. 函数设计规范

#### 函数长度限制
```javascript
// ✅ 函数保持简洁（建议<50行）
function validateOrder(order) {
    if (!order) return false;
    if (!order.customer) return false;
    if (!order.pickup) return false;
    return true;
}

// ❌ 避免过长函数（>100行）
function processComplexOrder(order) {
    // 100+ 行复杂逻辑...
    // 应该拆分为多个小函数
}
```

#### 参数设计
```javascript
// ✅ 参数数量合理（建议<5个）
function createOrder(customer, pickup, dropoff, date, time) {
    // 实现
}

// ✅ 使用对象参数处理复杂情况
function createOrderAdvanced(orderConfig) {
    const {
        customer,
        pickup,
        dropoff,
        date,
        time,
        options = {}
    } = orderConfig;
}

// ❌ 避免过多参数
function createOrderBad(customer, pickup, dropoff, date, time, price, currency, carType, language, specialReqs) {
    // 太多参数，难以维护
}
```

### 3. 注释规范

#### JSDoc注释
```javascript
/**
 * @OTA_SERVICE 订单验证服务
 * 🏷️ 标签: @OTA_VALIDATION_SERVICE
 * 📝 功能: 验证订单数据的完整性和有效性
 * ⚠️ 警告: 已注册，请勿重复开发
 * 
 * @param {Object} order - 订单对象
 * @param {string} order.customer - 客户姓名
 * @param {string} order.pickup - 上车地点
 * @param {string} order.dropoff - 目的地
 * @returns {ValidationResult} 验证结果
 * 
 * @example
 * const result = validateOrder({
 *     customer: '张三',
 *     pickup: '机场',
 *     dropoff: '酒店'
 * });
 */
function validateOrder(order) {
    // 实现
}
```

#### 内联注释
```javascript
function processOrder(order) {
    // 验证订单基本信息
    const isValid = validateOrder(order);
    if (!isValid) {
        return { success: false, error: 'Invalid order data' };
    }
    
    // 处理特殊要求
    if (order.specialRequirements) {
        this.processSpecialRequirements(order);
    }
    
    // 提交到后端API
    return this.apiService.createOrder(order);
}
```

## 🚀 性能规范

### 1. 服务实例缓存
```javascript
// ✅ 在构造函数中缓存服务
class MyService {
    constructor() {
        this.logger = getLogger();        // 缓存logger实例
        this.appState = getAppState();    // 缓存状态实例
    }
    
    someMethod() {
        this.logger.log('操作完成');      // 使用缓存实例
    }
}

// ❌ 避免重复获取服务
class MyService {
    someMethod() {
        const logger = getLogger();       // 每次都重新获取
        logger.log('操作完成');
    }
}
```

### 2. 配置对象提取
```javascript
// ✅ 提取重复配置
class APIService {
    constructor() {
        this.defaultConfig = {
            timeout: 5000,
            retries: 3,
            headers: {
                'Content-Type': 'application/json'
            }
        };
    }
    
    makeRequest(url, options = {}) {
        const config = { ...this.defaultConfig, ...options };
        return fetch(url, config);
    }
}

// ❌ 避免重复配置
class APIService {
    makeRequest(url) {
        return fetch(url, {
            timeout: 5000,     // 重复配置
            retries: 3,        // 重复配置
            headers: {
                'Content-Type': 'application/json'  // 重复配置
            }
        });
    }
}
```

### 3. 事件处理优化
```javascript
// ✅ 使用事件委托
class OrderUI {
    constructor() {
        this.container = document.getElementById('orderContainer');
        this.setupEventListeners();
    }
    
    setupEventListeners() {
        // 事件委托，一个监听器处理多个元素
        this.container.addEventListener('click', (event) => {
            if (event.target.matches('.order-edit-btn')) {
                this.handleEditClick(event);
            } else if (event.target.matches('.order-delete-btn')) {
                this.handleDeleteClick(event);
            }
        });
    }
}

// ❌ 避免为每个元素单独绑定
class OrderUI {
    setupEventListeners() {
        document.querySelectorAll('.order-edit-btn').forEach(btn => {
            btn.addEventListener('click', this.handleEditClick); // 多个监听器
        });
    }
}
```

## 🔒 安全规范

### 1. 输入验证
```javascript
// ✅ 始终验证用户输入
function processOrderInput(orderText) {
    // 验证输入
    if (!orderText || typeof orderText !== 'string') {
        throw new Error('Invalid order text');
    }
    
    if (orderText.length > 10000) {
        throw new Error('Order text too long');
    }
    
    // 清理输入
    const cleanText = orderText.trim().replace(/[<>]/g, '');
    
    return this.processOrder(cleanText);
}
```

### 2. API密钥处理
```javascript
// ✅ 项目内部使用的API密钥处理（根据CLAUDE.md说明）
class APIService {
    constructor() {
        // 内部使用，API密钥可以硬编码（已确认）
        this.apiKey = 'your-api-key';
    }
}
```

### 3. 错误处理
```javascript
// ✅ 完善的错误处理
async function callAPI(url, data) {
    try {
        const response = await fetch(url, {
            method: 'POST',
            body: JSON.stringify(data)
        });
        
        if (!response.ok) {
            throw new Error(`API call failed: ${response.status} ${response.statusText}`);
        }
        
        return await response.json();
    } catch (error) {
        this.logger.logError('API调用失败', error);
        
        // 提供用户友好的错误信息
        throw new Error('网络请求失败，请稍后重试');
    }
}
```

## 📝 测试规范

### 1. 单元测试
```javascript
// ✅ 为核心业务逻辑编写测试
function testOrderValidation() {
    const validOrder = {
        customer: '张三',
        pickup: '机场',
        dropoff: '酒店'
    };
    
    const result = validateOrder(validOrder);
    console.assert(result.isValid === true, 'Valid order should pass validation');
    
    const invalidOrder = {};
    const result2 = validateOrder(invalidOrder);
    console.assert(result2.isValid === false, 'Invalid order should fail validation');
}
```

### 2. 集成测试
```javascript
// ✅ 测试组件间的集成
async function testOrderFlow() {
    // 1. 创建订单
    const orderManager = getOrderManager();
    const order = await orderManager.createOrder(testOrderData);
    
    // 2. 验证状态变化
    const appState = getAppState();
    const currentOrder = appState.get('currentOrder');
    console.assert(currentOrder.id === order.id, 'Order should be saved to state');
    
    // 3. 验证UI更新
    const orderElement = document.querySelector(`#order-${order.id}`);
    console.assert(orderElement !== null, 'Order should be displayed in UI');
}
```

## 🚨 禁止模式

### 1. 绝对禁止的模式
```javascript
// ❌ 绝对禁止：重复定义已存在的全局函数
function getLogger() {
    // 这会与现有的getLogger冲突
}

// ❌ 绝对禁止：直接修改全局对象
Array.prototype.customMethod = function() {};  // 污染原型
window.alert = function() {};                   // 覆盖原生方法

// ❌ 绝对禁止：忽略错误
try {
    riskyOperation();
} catch (error) {
    // 静默忽略错误
}

// ❌ 绝对禁止：使用eval或Function构造器
eval('someCode');                               // 安全风险
new Function('return ' + userInput)();          // 安全风险
```

### 2. 强烈不推荐的模式
```javascript
// ⚠️ 强烈不推荐：深度嵌套
if (condition1) {
    if (condition2) {
        if (condition3) {
            if (condition4) {
                if (condition5) {
                    // 嵌套过深，难以理解
                }
            }
        }
    }
}

// ⚠️ 强烈不推荐：魔术数字
setTimeout(() => {}, 3600000);                  // 使用常量: const HOUR = 3600000;
if (userLevel > 5) {}                           // 使用常量: const VIP_LEVEL = 5;

// ⚠️ 强烈不推荐：过度简化的变量名
const d = new Date();                           // 使用: const currentDate
const u = getUserInfo();                        // 使用: const userInfo
const tmp = processData();                      // 使用: const processedData
```

## 🔧 工具配置

### 1. VS Code配置
```json
{
    "editor.tabSize": 4,
    "editor.insertSpaces": true,
    "editor.formatOnSave": true,
    "files.trimTrailingWhitespace": true,
    "files.insertFinalNewline": true
}
```

### 2. ESLint规则（建议）
```javascript
module.exports = {
    rules: {
        // 代码质量
        "no-unused-vars": "error",
        "no-undef": "error",
        "no-duplicate-imports": "error",
        
        // 风格规范
        "indent": ["error", 4],
        "quotes": ["error", "single"],
        "semi": ["error", "always"],
        
        // OTA特定规则
        "no-implicit-globals": "error",        // 防止全局污染
        "prefer-const": "error",               // 优先使用const
        "no-var": "error"                      // 禁用var，使用let/const
    }
};
```

## 📊 质量检查清单

### 代码提交前检查
- [ ] 运行`detectDuplicates()`确认无重复函数
- [ ] 运行`architectureReport()`确认架构健康
- [ ] 所有函数都有适当的@OTA_标签
- [ ] 所有服务都注册到OTA.Registry
- [ ] 遵循命名规范和代码风格
- [ ] 包含必要的错误处理
- [ ] 编写了相应的测试

### 代码审查检查
- [ ] 代码逻辑清晰易懂
- [ ] 没有重复代码
- [ ] 性能优化到位
- [ ] 安全性考虑充分
- [ ] 遵循项目架构原则

---

## 📚 参考资源

- [开发指南](DEVELOPMENT_GUIDE.md)
- [API文档](API-Documentation.md)
- [项目架构](../memory-bank/code_structure.md)
- [PR检查清单](../.github/PULL_REQUEST_TEMPLATE.md)

**记住**: 好的代码是写给人读的，机器只是顺便执行它。优雅、简洁、可维护的代码是我们的目标。