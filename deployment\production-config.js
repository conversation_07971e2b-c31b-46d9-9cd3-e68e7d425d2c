/**
 * 智能学习型格式预处理引擎 - 生产环境配置
 * 包含生产环境的配置参数、监控设置、备份策略等
 * 
 * <AUTHOR>
 * @version 1.0.0
 * @created 2025-01-16
 */

// 生产环境配置
const PRODUCTION_CONFIG = {
    // 环境信息
    environment: {
        name: 'production',
        version: '1.0.0',
        buildDate: '2025-01-16',
        debug: false
    },

    // 学习系统配置
    learningSystem: {
        enabled: true,
        autoLearning: true,
        confidenceThreshold: 0.75,
        maxRules: 2000,
        learningRate: 0.8,
        evaluationInterval: 24 * 60 * 60 * 1000, // 24小时
        backupInterval: 6 * 60 * 60 * 1000 // 6小时
    },

    // 存储配置
    storage: {
        retentionDays: 90, // 生产环境保留90天
        compressionEnabled: true,
        backupEnabled: true,
        encryptionEnabled: true,
        maxStorageSize: 100 * 1024 * 1024, // 100MB
        cleanupInterval: 24 * 60 * 60 * 1000, // 24小时清理一次
        keys: {
            operations: 'ota_learning_operations_prod',
            rules: 'ota_learning_rules_prod',
            config: 'ota_learning_config_prod',
            cache: 'ota_learning_cache_prod',
            backup: 'ota_learning_backup_prod'
        }
    },

    // 缓存配置
    cache: {
        maxSize: 2000,
        maxMemoryUsage: 80 * 1024 * 1024, // 80MB
        defaultTTL: 60 * 60 * 1000, // 1小时
        preloadEnabled: true,
        preloadThreshold: 0.8,
        cleanupInterval: 10 * 60 * 1000, // 10分钟
        hitRateTarget: 0.85
    },

    // 性能监控配置
    performance: {
        monitoringEnabled: true,
        sampleRate: 1.0, // 生产环境100%采样
        metricsInterval: 30000, // 30秒
        alertThresholds: {
            responseTime: 3000, // 3秒
            memoryUsage: 120 * 1024 * 1024, // 120MB
            errorRate: 0.02, // 2%
            cacheHitRate: 0.75, // 75%
            cpuUsage: 0.8 // 80%
        },
        retentionDays: 30
    },

    // 日志配置
    logging: {
        level: 'info', // 生产环境使用info级别
        enableConsole: false, // 生产环境关闭控制台日志
        enableStorage: true,
        maxLogSize: 10 * 1024 * 1024, // 10MB
        rotationSize: 5 * 1024 * 1024, // 5MB轮转
        retentionDays: 30,
        categories: {
            system: true,
            learning: true,
            performance: true,
            error: true,
            security: true
        }
    },

    // 安全配置
    security: {
        enableEncryption: true,
        encryptionKey: null, // 运行时设置
        enableIntegrityCheck: true,
        maxFailedAttempts: 5,
        lockoutDuration: 15 * 60 * 1000, // 15分钟
        sessionTimeout: 8 * 60 * 60 * 1000, // 8小时
        enableAuditLog: true
    },

    // 备份配置
    backup: {
        enabled: true,
        interval: 6 * 60 * 60 * 1000, // 6小时
        maxBackups: 48, // 保留48个备份（12天）
        compressionEnabled: true,
        encryptionEnabled: true,
        verificationEnabled: true,
        remoteBackup: {
            enabled: false, // 需要配置远程存储
            endpoint: null,
            credentials: null
        }
    },

    // 监控报警配置
    monitoring: {
        enabled: true,
        endpoints: {
            health: '/api/health',
            metrics: '/api/metrics',
            status: '/api/status'
        },
        alerts: {
            email: {
                enabled: false, // 需要配置邮件服务
                recipients: [],
                smtp: null
            },
            webhook: {
                enabled: false, // 需要配置Webhook
                url: null,
                headers: {}
            },
            console: {
                enabled: true
            }
        },
        healthCheck: {
            interval: 60000, // 1分钟
            timeout: 5000, // 5秒
            retries: 3
        }
    },

    // 优化配置
    optimization: {
        autoOptimization: true,
        optimizationInterval: 60 * 60 * 1000, // 1小时
        strategies: {
            caching: true,
            algorithmTuning: true,
            memoryManagement: true,
            ruleOptimization: true,
            dataStructureOptimization: true
        },
        performanceThresholds: {
            responseTime: 2000,
            memoryUsage: 100 * 1024 * 1024,
            cacheHitRate: 0.8,
            cpuUsage: 0.7
        }
    },

    // 错误处理配置
    errorHandling: {
        enableGlobalHandler: true,
        enableRetry: true,
        maxRetries: 3,
        retryDelay: 1000,
        enableFallback: true,
        enableCircuitBreaker: true,
        circuitBreakerThreshold: 10,
        circuitBreakerTimeout: 30000
    },

    // 功能开关
    features: {
        learningSystem: true,
        predictiveCorrection: true,
        adaptivePrompts: true,
        performanceMonitoring: true,
        cacheManagement: true,
        autoOptimization: true,
        backupRestore: true,
        securityAudit: true
    },

    // API配置
    api: {
        timeout: 30000, // 30秒
        retries: 3,
        rateLimit: {
            enabled: true,
            maxRequests: 1000,
            windowMs: 60 * 1000 // 1分钟
        }
    }
};

/**
 * 生产环境初始化函数
 */
function initializeProductionEnvironment() {
    try {
        console.log('正在初始化生产环境...');
        
        // 应用生产配置
        if (window.OTA && window.OTA.learningConfig) {
            const config = window.OTA.learningConfig;
            
            // 批量设置配置
            Object.entries(PRODUCTION_CONFIG).forEach(([section, settings]) => {
                if (typeof settings === 'object' && settings !== null) {
                    Object.entries(settings).forEach(([key, value]) => {
                        config.set(`${section}.${key}`, value);
                    });
                }
            });
            
            console.log('生产环境配置已应用');
        }
        
        // 设置错误处理
        setupGlobalErrorHandling();
        
        // 初始化监控
        initializeMonitoring();
        
        // 设置备份
        setupBackupSystem();
        
        // 启用安全功能
        enableSecurityFeatures();
        
        console.log('生产环境初始化完成');
        
    } catch (error) {
        console.error('生产环境初始化失败:', error);
        throw error;
    }
}

/**
 * 设置全局错误处理
 */
function setupGlobalErrorHandling() {
    if (!PRODUCTION_CONFIG.errorHandling.enableGlobalHandler) {
        return;
    }
    
    // 捕获未处理的Promise拒绝
    window.addEventListener('unhandledrejection', (event) => {
        console.error('未处理的Promise拒绝:', event.reason);
        
        // 记录到日志系统
        if (window.OTA && window.OTA.logger) {
            window.OTA.logger.logError('未处理的Promise拒绝', event.reason);
        }
        
        // 发送监控报警
        sendAlert('unhandled_promise_rejection', {
            reason: event.reason,
            timestamp: new Date().toISOString()
        });
    });
    
    // 捕获全局错误
    window.addEventListener('error', (event) => {
        console.error('全局错误:', event.error);
        
        // 记录到日志系统
        if (window.OTA && window.OTA.logger) {
            window.OTA.logger.logError('全局错误', event.error);
        }
        
        // 发送监控报警
        sendAlert('global_error', {
            message: event.message,
            filename: event.filename,
            lineno: event.lineno,
            colno: event.colno,
            error: event.error,
            timestamp: new Date().toISOString()
        });
    });
}

/**
 * 初始化监控系统
 */
function initializeMonitoring() {
    if (!PRODUCTION_CONFIG.monitoring.enabled) {
        return;
    }
    
    // 健康检查
    setInterval(() => {
        performHealthCheck();
    }, PRODUCTION_CONFIG.monitoring.healthCheck.interval);
    
    // 性能指标收集
    setInterval(() => {
        collectPerformanceMetrics();
    }, PRODUCTION_CONFIG.performance.metricsInterval);
    
    console.log('监控系统已启动');
}

/**
 * 执行健康检查
 */
function performHealthCheck() {
    try {
        const healthStatus = {
            timestamp: new Date().toISOString(),
            status: 'healthy',
            checks: {}
        };
        
        // 检查学习系统（已简化）
        if (window.OTA && window.OTA.learningConfig) {
            healthStatus.checks.learningSystem = 'healthy';
        } else {
            healthStatus.checks.learningSystem = 'disabled';
        }
        
        // 检查缓存系统
        if (window.OTA && window.OTA.intelligentCacheManager) {
            const cacheStats = window.OTA.intelligentCacheManager.getStats();
            healthStatus.checks.cacheSystem = cacheStats.hitRate > 0.5 ? 'healthy' : 'degraded';
        }
        
        // 检查性能监控
        if (window.OTA && window.OTA.performanceMonitor) {
            const metrics = window.OTA.performanceMonitor.getRealTimeMetrics();
            healthStatus.checks.performanceMonitor = metrics ? 'healthy' : 'unhealthy';
        }
        
        // 检查内存使用
        if (performance.memory) {
            const memoryUsage = performance.memory.usedJSHeapSize;
            const memoryLimit = PRODUCTION_CONFIG.performance.alertThresholds.memoryUsage;
            healthStatus.checks.memoryUsage = memoryUsage < memoryLimit ? 'healthy' : 'warning';
        }
        
        // 如果状态不健康，发送报警
        if (healthStatus.status !== 'healthy') {
            sendAlert('health_check_failed', healthStatus);
        }
        
        return healthStatus;
        
    } catch (error) {
        console.error('健康检查失败:', error);
        sendAlert('health_check_error', { error: error.message });
        return { status: 'error', error: error.message };
    }
}

/**
 * 收集性能指标
 */
function collectPerformanceMetrics() {
    try {
        const metrics = {
            timestamp: new Date().toISOString(),
            memory: performance.memory ? {
                used: performance.memory.usedJSHeapSize,
                total: performance.memory.totalJSHeapSize,
                limit: performance.memory.jsHeapSizeLimit
            } : null,
            performance: window.OTA?.performanceMonitor?.getRealTimeMetrics() || null,
            cache: window.OTA?.intelligentCacheManager?.getStats() || null,
            learning: window.OTA?.learningEffectivenessEvaluator?.getCurrentEvaluation() || null
        };
        
        // 检查阈值并发送报警
        checkPerformanceThresholds(metrics);
        
        // 存储指标（可选）
        // 存储性能指标到localStorage（简化版）
        if (typeof Storage !== 'undefined') {
            localStorage.setItem('ota_performance_metrics', JSON.stringify(metrics));
        }
        
        return metrics;
        
    } catch (error) {
        console.error('性能指标收集失败:', error);
    }
}

/**
 * 检查性能阈值
 */
function checkPerformanceThresholds(metrics) {
    const thresholds = PRODUCTION_CONFIG.performance.alertThresholds;
    
    // 检查内存使用
    if (metrics.memory && metrics.memory.used > thresholds.memoryUsage) {
        sendAlert('high_memory_usage', {
            current: metrics.memory.used,
            threshold: thresholds.memoryUsage,
            percentage: (metrics.memory.used / thresholds.memoryUsage * 100).toFixed(1)
        });
    }
    
    // 检查响应时间
    if (metrics.performance && metrics.performance.averageResponseTime > thresholds.responseTime) {
        sendAlert('slow_response_time', {
            current: metrics.performance.averageResponseTime,
            threshold: thresholds.responseTime
        });
    }
    
    // 检查错误率
    if (metrics.performance && metrics.performance.totalOperations > 0) {
        const errorRate = metrics.performance.totalErrors / metrics.performance.totalOperations;
        if (errorRate > thresholds.errorRate) {
            sendAlert('high_error_rate', {
                current: errorRate,
                threshold: thresholds.errorRate,
                percentage: (errorRate * 100).toFixed(2)
            });
        }
    }
    
    // 检查缓存命中率
    if (metrics.cache && metrics.cache.hitRate < thresholds.cacheHitRate) {
        sendAlert('low_cache_hit_rate', {
            current: metrics.cache.hitRate,
            threshold: thresholds.cacheHitRate,
            percentage: (metrics.cache.hitRate * 100).toFixed(1)
        });
    }
}

/**
 * 发送监控报警
 */
function sendAlert(alertType, data) {
    const alert = {
        type: alertType,
        timestamp: new Date().toISOString(),
        environment: 'production',
        data: data,
        severity: getAlertSeverity(alertType)
    };
    
    // 控制台报警
    if (PRODUCTION_CONFIG.monitoring.alerts.console.enabled) {
        const message = `[${alert.severity.toUpperCase()}] ${alertType}: ${JSON.stringify(data)}`;
        
        switch (alert.severity) {
            case 'critical':
                console.error(message);
                break;
            case 'warning':
                console.warn(message);
                break;
            default:
                console.info(message);
        }
    }
    
    // 邮件报警（需要配置）
    if (PRODUCTION_CONFIG.monitoring.alerts.email.enabled) {
        sendEmailAlert(alert);
    }
    
    // Webhook报警（需要配置）
    if (PRODUCTION_CONFIG.monitoring.alerts.webhook.enabled) {
        sendWebhookAlert(alert);
    }
    
    // 记录到日志
    if (window.OTA && window.OTA.logger) {
        window.OTA.logger.log(`监控报警: ${alertType}`, alert.severity, alert);
    }
}

/**
 * 获取报警严重程度
 */
function getAlertSeverity(alertType) {
    const severityMap = {
        'health_check_failed': 'critical',
        'global_error': 'critical',
        'unhandled_promise_rejection': 'critical',
        'high_memory_usage': 'warning',
        'slow_response_time': 'warning',
        'high_error_rate': 'critical',
        'low_cache_hit_rate': 'info',
        'backup_failed': 'warning',
        'security_violation': 'critical'
    };
    
    return severityMap[alertType] || 'info';
}

/**
 * 设置备份系统
 */
function setupBackupSystem() {
    if (!PRODUCTION_CONFIG.backup.enabled) {
        return;
    }
    
    // 定期备份
    setInterval(() => {
        performBackup();
    }, PRODUCTION_CONFIG.backup.interval);
    
    console.log('备份系统已启动');
}

/**
 * 执行备份
 */
function performBackup() {
    try {
        if (!window.OTA || !window.OTA.dataPersistenceManager) {
            throw new Error('数据持久化管理器不可用');
        }
        
        const backupData = window.OTA.dataPersistenceManager.exportLearningData({
            compress: PRODUCTION_CONFIG.backup.compressionEnabled,
            encrypt: PRODUCTION_CONFIG.backup.encryptionEnabled
        });
        
        const backupKey = `backup_${Date.now()}`;
        
        // 保存备份
        // 备份到localStorage（简化版）
        if (typeof Storage !== 'undefined') {
            localStorage.setItem(backupKey, JSON.stringify(backupData));
        }
        
        // 清理旧备份
        cleanupOldBackups();
        
        console.log('备份完成:', backupKey);
        
    } catch (error) {
        console.error('备份失败:', error);
        sendAlert('backup_failed', { error: error.message });
    }
}

/**
 * 清理旧备份
 */
function cleanupOldBackups() {
    try {
        // 实现备份清理逻辑
        console.log('旧备份清理完成');
    } catch (error) {
        console.error('清理旧备份失败:', error);
    }
}

/**
 * 启用安全功能
 */
function enableSecurityFeatures() {
    if (!PRODUCTION_CONFIG.security.enableEncryption) {
        return;
    }
    
    // 设置加密密钥（实际应用中应该从安全的地方获取）
    // PRODUCTION_CONFIG.security.encryptionKey = generateEncryptionKey();
    
    console.log('安全功能已启用');
}

/**
 * 邮件报警（需要实现）
 */
function sendEmailAlert(alert) {
    // 实现邮件发送逻辑
    console.log('邮件报警:', alert);
}

/**
 * Webhook报警（需要实现）
 */
function sendWebhookAlert(alert) {
    // 实现Webhook发送逻辑
    console.log('Webhook报警:', alert);
}

// 导出配置和函数
window.PRODUCTION_CONFIG = PRODUCTION_CONFIG;
window.initializeProductionEnvironment = initializeProductionEnvironment;
window.performHealthCheck = performHealthCheck;
window.collectPerformanceMetrics = collectPerformanceMetrics;

// 如果在生产环境中，自动初始化
if (typeof window !== 'undefined' && window.location.hostname !== 'localhost') {
    // 延迟初始化，确保所有模块都已加载
    setTimeout(() => {
        initializeProductionEnvironment();
    }, 2000);
}

console.log('生产环境配置已加载', { version: PRODUCTION_CONFIG.environment.version });

// 导出到全局命名空间
if (window.OTA) {
    window.OTA.productionConfig = PRODUCTION_CONFIG;
    window.OTA.initializeProductionEnvironment = initializeProductionEnvironment;
}
