/**
 * @OTA_CONFIG Learning Engine Basic Configuration
 * 🏷️ 标签: @OTA_LEARNING_CONFIG
 * 📝 说明: 基础学习引擎配置，已简化from复杂版本
 * ⚠️ 警告: 已注册，请勿重复开发
 */

// 确保OTA命名空间存在
window.OTA = window.OTA || {};

(function() {
    'use strict';

    /**
     * 简化的学习引擎配置
     * 移除过度复杂的配置，只保留基础功能
     */
    const LearningConfig = {
        // 基础配置
        enabled: true,
        version: '2.0-simplified',
        
        // 存储配置
        storageKey: 'ota-learning-data',
        maxStorageSize: 1 * 1024 * 1024, // 1MB足够
        
        // 简化的功能开关
        features: {
            userOperationLearning: false,    // 关闭复杂的用户操作学习
            errorClassification: false,       // 关闭25种错误分类
            patternMatching: false,          // 关闭复杂模式匹配
            predictiveCorrection: false      // 关闭预测纠错
        },
        
        // 基础验证
        validate() {
            return {
                isValid: true,
                version: this.version,
                featuresEnabled: Object.values(this.features).filter(Boolean).length,
                message: '简化配置已就绪'
            };
        },
        
        // 获取配置
        getConfig(key) {
            return key ? this[key] : this;
        }
    };

    // 单例工厂函数
    let instance = LearningConfig;
    
    /**
     * @OTA_FACTORY 学习配置工厂函数
     * 🏷️ 标签: @OTA_LEARNING_CONFIG_FACTORY
     */
    function getLearningConfig() {
        return instance;
    }

    // 注册到OTA命名空间
    window.OTA.LearningConfig = LearningConfig;
    window.OTA.getLearningConfig = getLearningConfig;
    window.OTA.learningConfig = instance;
    
    // 向后兼容
    window.LearningConfig = LearningConfig;
    window.getLearningConfig = getLearningConfig;
    window.learningConfig = instance;

    // 初始化日志
    const logger = window.getLogger ? window.getLogger() : console;
    if (logger && logger.log) {
        logger.log('🚀 简化学习引擎配置已加载', 'info', {
            version: LearningConfig.version,
            features: LearningConfig.features
        });
    }

})();